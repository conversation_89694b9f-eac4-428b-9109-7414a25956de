import pygame
import time
import json
from cars import Car, OpponentCar
from background import Background
from map import Map
from ui import TimeToStart, draw_end_screen, draw_profile_screen, draw_garage_screen
from garage_screen import draw_enhanced_garage_screen
from shop_screen import draw_shop_screen
from ui_components import TextButton
from save_ui import draw_save_menu
from save_system import save_system
from level_info import draw_level_info_screen, draw_level_complete_screen
from level_up_screen import draw_level_up_screen
from valuation_system import valuation_system
from selling_system import draw_garage_with_selling
from fuel_system import fuel_system
from tire_system import tire_system

from parts_requirements import parts_requirements
from utils import load_font, load_image, resource_path
from cursor_manager import cursor_manager
import sys

class Game:
    def __init__(self):
        pygame.init()
        pygame.font.init()
        self.screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
        self.s_width, self.s_height = self.screen.get_size()
        self.is_fullscreen = True  # Track fullscreen state
        pygame.display.set_caption('Xtreme Cars')
        pygame.display.set_icon(load_image('assets/img/icon.png'))
        self.clock = pygame.time.Clock()
        self.running = True

        # Initialize resolution manager
        try:
            from resolution_manager import init_resolution_manager
            init_resolution_manager(self.s_width, self.s_height)
        except ImportError:
            pass  # Resolution manager not available

    def calculate_exp_required_for_level(self, level):
        """Calculate the total EXP required to reach a specific level using polynomial growth"""
        if level <= 1:
            return 0
        # Polynomial growth: base + (level^1.8 * 50) for more challenging progression
        # Level 2: ~141, Level 10: ~1581, Level 50: ~17678, Level 100: ~50000
        return int(100 + (level ** 1.8) * 50)

    def fix_level_progression(self, user_data):
        """Fix corrupted level progression data"""
        current_level = user_data['level']['current']
        current_exp = user_data['level']['exp']

        # Calculate what the required_to_next_level should be
        correct_required = self.calculate_exp_required_for_level(current_level + 1)

        # Check if player should have leveled up already
        while current_exp >= correct_required and correct_required > 0:
            current_level += 1
            correct_required = self.calculate_exp_required_for_level(current_level + 1)

        # Update the user data with corrected values
        user_data['level']['current'] = current_level
        user_data['level']['required_to_next_level'] = correct_required

        return user_data

    def update_race_data_after_finish(self, user_data, player_level, player_exp, player_money, selected_car_index, selected_car_data, race_time, driving_style="normal_driving"):
        """Update all race-related data after race completion"""
        # Update usage data after race
        if "usage_data" not in user_data:
            user_data["usage_data"] = {"cars": {}}

        if "cars" not in user_data["usage_data"]:
            user_data["usage_data"]["cars"] = {}

        if str(selected_car_index) not in user_data["usage_data"]["cars"]:
            user_data["usage_data"]["cars"][str(selected_car_index)] = valuation_system.get_default_usage_data()

        # Update usage data (race completed)
        car_usage = user_data["usage_data"]["cars"][str(selected_car_index)]
        user_data["usage_data"]["cars"][str(selected_car_index)] = valuation_system.update_usage_data(car_usage, 1)

        # Consume fuel based on race time
        if race_time and race_time > 0:
            fuel_system.consume_fuel(selected_car_index, race_time, selected_car_data, car_usage)
            # Apply tire wear based on race time
            tire_system.apply_tire_wear(selected_car_index, race_time, selected_car_data, driving_style)

        # Update user_data dictionary with new values
        user_data['level']['current'] = player_level
        user_data['level']['exp'] = player_exp
        user_data['level']['required_to_next_level'] = self.calculate_exp_required_for_level(player_level + 1)
        user_data['money'] = player_money

        # Save updated user_data back to profile.json
        with open(resource_path('data/profile.json'), 'w', encoding='utf-8') as f:
            json.dump(user_data, f, indent=4)

        # Auto-save if current save slot is set
        if save_system.current_save_slot:
            save_system.save_game(save_system.current_save_slot)

    def calculate_level_up_rewards(self, new_level):
        """Calculate rewards for reaching a new level"""
        rewards = {
            'money': 0,
            'unlocked_parts': [],
            'unlocked_cars': [],
            'achievements': [],
            'special_bonuses': []
        }

        # Base money reward (scales with level)
        rewards['money'] = int(200 + (new_level * 150) + (new_level ** 1.5 * 25))

        # Unlock parts at specific levels
        part_unlocks = {
            5: [("engine", "Tuned I4")],
            10: [("turbo", "Small Turbo")],
            15: [("engine", "V6 Naturally Aspirated"), ("intercooler", "Large Front Mount")],
            20: [("ecu", "Stage 2 Tune")],
            25: [("turbo", "Medium Turbo")],
            30: [("engine", "V6 Twin Turbo"), ("intercooler", "Top Mount")],
            35: [("ecu", "Stage 3 Tune")],
            40: [("turbo", "Large Turbo"), ("intercooler", "Water-to-Air")],
            45: [("engine", "V8 Muscle")],
            50: [("turbo", "Ball Bearing Turbo"), ("ecu", "Custom Dyno Tune")],
            60: [("engine", "V8 High Performance"), ("intercooler", "Race Spec Intercooler")],
            70: [("turbo", "Variable Geometry Turbo"), ("ecu", "Race ECU")],
            80: [("engine", "V10 Supercar"), ("turbo", "Twin Turbo Setup")],
            90: [("ecu", "Pro Tuner ECU")],
            100: [("engine", "V12 Hypercar")]
        }

        if new_level in part_unlocks:
            rewards['unlocked_parts'] = part_unlocks[new_level]

        # Achievement milestones
        achievement_levels = {
            10: "Rookie Racer",
            25: "Street Veteran",
            50: "Racing Pro",
            75: "Speed Demon",
            100: "Racing Legend"
        }

        if new_level in achievement_levels:
            rewards['achievements'].append(achievement_levels[new_level])

        # Special bonuses for major milestones
        if new_level % 25 == 0:  # Every 25 levels
            bonus_money = new_level * 100
            rewards['special_bonuses'].append(f"Milestone Bonus: +{bonus_money}$")
            rewards['money'] += bonus_money

        return rewards

    def handle_level_up(self, player_exp, player_level, user_data):
        """Handle level progression correctly with rewards"""
        levels_gained = []
        total_rewards = {
            'money': 0,
            'unlocked_parts': [],
            'unlocked_cars': [],
            'achievements': [],
            'special_bonuses': []
        }

        # Keep leveling up until EXP is below the requirement for next level
        while True:
            required_for_next = self.calculate_exp_required_for_level(player_level + 1)
            if player_exp < required_for_next:
                break

            player_level += 1
            levels_gained.append(player_level)

            # Calculate rewards for this level
            level_rewards = self.calculate_level_up_rewards(player_level)

            # Accumulate rewards
            total_rewards['money'] += level_rewards['money']
            total_rewards['unlocked_parts'].extend(level_rewards['unlocked_parts'])
            total_rewards['unlocked_cars'].extend(level_rewards['unlocked_cars'])
            total_rewards['achievements'].extend(level_rewards['achievements'])
            total_rewards['special_bonuses'].extend(level_rewards['special_bonuses'])

        # Calculate final required_for_next after all level ups
        required_for_next = self.calculate_exp_required_for_level(player_level + 1)

        # Apply rewards to user data
        if levels_gained:
            user_data['money'] += total_rewards['money']

            # Add unlocked parts to inventory
            if 'inventory' not in user_data:
                user_data['inventory'] = {'owned_parts': {'engine': [], 'turbo': [], 'intercooler': [], 'ecu': []}}

            for part_type, part_name in total_rewards['unlocked_parts']:
                if part_type in user_data['inventory']['owned_parts']:
                    if part_name not in user_data['inventory']['owned_parts'][part_type]:
                        user_data['inventory']['owned_parts'][part_type].append(part_name)

        return player_level, required_for_next, levels_gained, total_rewards

    def run(self):
        # Initialize parts requirements system on startup
        try:
            cars_fixed, _ = parts_requirements.fix_all_cars_in_garage()
            if cars_fixed > 0:
                print(f"Startup: Fixed {cars_fixed} cars with missing required parts")
        except Exception as e:
            print(f"Error during startup parts validation: {e}")

        self.main_menu()

    def main_menu(self):
        # Get resolution manager for responsive scaling
        try:
            from resolution_manager import get_resolution_manager
            resolution_manager = get_resolution_manager()

            # Get responsive scaling values
            button_font_size = resolution_manager.get_scaled_font_size(36)
            button_width, button_height = resolution_manager.get_scaled_button_size(250, 60)
            button_spacing = resolution_manager.get_scaled_spacing(75)
            title_font_size = resolution_manager.get_scaled_font_size(48)

        except ImportError:
            # Fallback values if resolution manager not available
            button_font_size = 36
            button_width, button_height = 250, 60
            button_spacing = 75
            title_font_size = 48

        # Calculate responsive button positions
        button_labels = ['Gra', 'Samochody', 'Handel', 'Ustawienia', 'Wyjdź z gry']
        button_actions = [self.game_menu, self.cars_menu, self.trade_menu, self.settings_main_menu, self.quit_game]

        # Calculate total height of all buttons and spacing
        total_buttons_height = len(button_labels) * button_height + (len(button_labels) - 1) * button_spacing

        # Reserve space for title and ensure proper spacing
        try:
            title_space = resolution_manager.get_scaled_spacing(120)  # Space for title and margin
            available_height = self.s_height - title_space
            start_y = title_space + (available_height - total_buttons_height) // 2
        except:
            title_space = 120
            available_height = self.s_height - title_space
            start_y = title_space + (available_height - total_buttons_height) // 2

        # Create buttons with responsive scaling
        buttons = []
        for i, (label, action) in enumerate(zip(button_labels, button_actions)):
            button_x = (self.s_width - button_width) // 2
            button_y = start_y + i * (button_height + button_spacing)

            button = TextButton(label, button_x, button_y,
                              font_size=button_font_size,
                              width=button_width, height=button_height,
                              action=action)
            buttons.append(button)

        background = Background('background', self.s_width, self.s_height)
        while self.running:
            mouse_pos = pygame.mouse.get_pos()

            # Reset cursor state for this frame
            cursor_manager.reset_frame()

            background.draw(self.screen)

            # Draw title with responsive scaling
            try:
                title_font = load_font("arial", title_font_size)
                title = title_font.render("Xtreme Cars", True, (255, 255, 255))
                title_y = resolution_manager.get_scaled_spacing(50)
                title_x = (self.s_width - title.get_width()) // 2
                self.screen.blit(title, (title_x, title_y))
            except:
                # Fallback title rendering
                title_font = load_font("arial", 48)
                title = title_font.render("Xtreme Cars", True, (255, 255, 255))
                title_x = (self.s_width - title.get_width()) // 2
                self.screen.blit(title, (title_x, 50))

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    return

                # Handle button events
                for button in buttons:
                    button.handle_event(event, mouse_pos)

            # Update button hover states
            for button in buttons:
                button.update(mouse_pos)
                button.draw(self.screen)

            # Update cursor based on hover states
            cursor_manager.update_cursor()

            pygame.display.update()
        pygame.quit()
        sys.exit()

    def quit_game(self):
        """Properly quit the game"""
        self.running = False

    def load_game_menu(self):
        """Show load game menu"""
        result = draw_save_menu(self.s_width, self.s_height, self.screen, mode="load")
        if result == "quit":
            self.running = False
        elif result == "loaded":
            # Game loaded successfully, show confirmation
            self.show_message("Gra została wczytana!")
        elif result == "new_game":
            # New game created
            self.show_message("Rozpoczęto nową grę!")

    def save_game_menu(self):
        """Show save game menu"""
        result = draw_save_menu(self.s_width, self.s_height, self.screen, mode="save")
        if result == "quit":
            self.running = False
        elif result == "saved":
            # Game saved successfully
            self.show_message("Gra została zapisana!")

    def game_menu(self):
        """Game submenu with game-related options"""
        buttons = [
            TextButton('Single Player', self.s_width // 2 - 100, self.s_height // 2 - 100, action=self.single_player),
            TextButton('Wczytaj Grę', self.s_width // 2 - 100, self.s_height // 2 - 25, action=self.load_game_menu),
            TextButton('Zapisz Grę', self.s_width // 2 - 100, self.s_height // 2 + 50, action=self.save_game_menu),
            TextButton('Powrót', self.s_width // 2 - 100, self.s_height // 2 + 125, action=lambda: None)  # Will break the loop
        ]

        background = Background('background', self.s_width, self.s_height)
        submenu_running = True

        while submenu_running and self.running:
            mouse_pos = pygame.mouse.get_pos()

            # Reset cursor state for this frame
            cursor_manager.reset_frame()

            background.draw(self.screen)

            # Draw title
            font = load_font("arial", 48)
            title = font.render("Menu Gry", True, (255, 255, 255))
            self.screen.blit(title, (self.s_width // 2 - title.get_width() // 2, 100))

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    submenu_running = False

                # Handle button events
                for button in buttons:
                    if button.handle_event(event, mouse_pos):
                        if button.label == 'Powrót':
                            submenu_running = False

            # Update button hover states
            for button in buttons:
                button.update(mouse_pos)
                button.draw(self.screen)

            # Update cursor based on hover states
            cursor_manager.update_cursor()

            pygame.display.update()

    def cars_menu(self):
        """Cars submenu with car-related options"""
        buttons = [
            TextButton('Wybór samochodu', self.s_width // 2 - 100, self.s_height // 2 - 50, action=lambda: draw_garage_screen(self.s_width, self.s_height, self.screen)),
            TextButton('Tuning (Garaż)', self.s_width // 2 - 100, self.s_height // 2 + 25, action=lambda: draw_enhanced_garage_screen(self.s_width, self.s_height, self.screen)),
            TextButton('Powrót', self.s_width // 2 - 100, self.s_height // 2 + 100, action=lambda: None)
        ]

        background = Background('background', self.s_width, self.s_height)
        submenu_running = True

        while submenu_running and self.running:
            mouse_pos = pygame.mouse.get_pos()

            # Reset cursor state for this frame
            cursor_manager.reset_frame()

            background.draw(self.screen)

            # Draw title
            font = load_font("arial", 48)
            title = font.render("Menu Samochodów", True, (255, 255, 255))
            self.screen.blit(title, (self.s_width // 2 - title.get_width() // 2, 100))

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    submenu_running = False

                # Handle button events
                for button in buttons:
                    if button.handle_event(event, mouse_pos):
                        if button.label == 'Powrót':
                            submenu_running = False

            # Update button hover states
            for button in buttons:
                button.update(mouse_pos)
                button.draw(self.screen)

            # Update cursor based on hover states
            cursor_manager.update_cursor()

            pygame.display.update()

    def trade_menu(self):
        """Trade submenu with shop and selling options"""
        buttons = [
            TextButton('Sklep', self.s_width // 2 - 100, self.s_height // 2 - 50, action=lambda: draw_shop_screen(self.s_width, self.s_height, self.screen)),
            TextButton('Sprzedaż', self.s_width // 2 - 100, self.s_height // 2 + 25, action=lambda: draw_garage_with_selling(self.s_width, self.s_height, self.screen)),
            TextButton('Powrót', self.s_width // 2 - 100, self.s_height // 2 + 100, action=lambda: None)
        ]

        background = Background('background', self.s_width, self.s_height)
        submenu_running = True

        while submenu_running and self.running:
            mouse_pos = pygame.mouse.get_pos()

            # Reset cursor state for this frame
            cursor_manager.reset_frame()

            background.draw(self.screen)

            # Draw title
            font = load_font("arial", 48)
            title = font.render("Menu Handlu", True, (255, 255, 255))
            self.screen.blit(title, (self.s_width // 2 - title.get_width() // 2, 100))

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    submenu_running = False

                # Handle button events
                for button in buttons:
                    if button.handle_event(event, mouse_pos):
                        if button.label == 'Powrót':
                            submenu_running = False

            # Update button hover states
            for button in buttons:
                button.update(mouse_pos)
                button.draw(self.screen)

            # Update cursor based on hover states
            cursor_manager.update_cursor()

            pygame.display.update()

    def settings_main_menu(self):
        """Settings submenu with profile and settings options"""
        buttons = [
            TextButton('Profil', self.s_width // 2 - 100, self.s_height // 2 - 50, action=lambda: draw_profile_screen(self.s_width, self.s_height, self.screen)),
            TextButton('Ustawienia gry', self.s_width // 2 - 100, self.s_height // 2 + 25, action=self.settings_menu),
            TextButton('Powrót', self.s_width // 2 - 100, self.s_height // 2 + 100, action=lambda: None)
        ]

        background = Background('background', self.s_width, self.s_height)
        submenu_running = True

        while submenu_running and self.running:
            mouse_pos = pygame.mouse.get_pos()

            # Reset cursor state for this frame
            cursor_manager.reset_frame()

            background.draw(self.screen)

            # Draw title
            font = load_font("arial", 48)
            title = font.render("Menu Ustawień", True, (255, 255, 255))
            self.screen.blit(title, (self.s_width // 2 - title.get_width() // 2, 100))

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    submenu_running = False

                # Handle button events
                for button in buttons:
                    if button.handle_event(event, mouse_pos):
                        if button.label == 'Powrót':
                            submenu_running = False

            # Update button hover states
            for button in buttons:
                button.update(mouse_pos)
                button.draw(self.screen)

            # Update cursor based on hover states
            cursor_manager.update_cursor()

            pygame.display.update()

    def settings_menu(self):
        """Settings menu with resolution and other options"""
        # Available resolutions
        resolutions = [
            (1024, 768, "1024x768"),
            (1280, 720, "1280x720 (HD)"),
            (1366, 768, "1366x768"),
            (1920, 1080, "1920x1080 (Full HD)"),
            (2560, 1440, "2560x1440 (QHD)"),
            ("fullscreen", "fullscreen", "Pełny ekran")
        ]

        # Create resolution buttons
        buttons = []
        for i, (width, height, label) in enumerate(resolutions):
            # Improved active resolution detection
            if width == "fullscreen":
                is_current = self.is_fullscreen
            else:
                # For windowed mode, check if resolution matches and we're not in fullscreen
                is_current = (not self.is_fullscreen and
                            self.s_width == width and
                            self.s_height == height)

            button_label = f"{'[AKTYWNE] ' if is_current else ''}{label}"

            # Fix lambda closure issue by creating a proper closure function
            def create_resolution_action(w, h):
                return lambda: self.change_resolution(w, h)

            button = TextButton(button_label, self.s_width // 2 - 150, 200 + i * 60,
                              font_size=24, width=300, height=50,
                              action=create_resolution_action(width, height))
            buttons.append(button)

        # Add back button
        back_button = TextButton('Powrót', self.s_width // 2 - 100, 200 + len(resolutions) * 60 + 50,
                                font_size=36, action=lambda: None)
        buttons.append(back_button)

        background = Background('background', self.s_width, self.s_height)
        submenu_running = True

        while submenu_running and self.running:
            mouse_pos = pygame.mouse.get_pos()

            # Reset cursor state for this frame
            cursor_manager.reset_frame()

            background.draw(self.screen)

            # Draw title
            font = load_font("arial", 48)
            title = font.render("Ustawienia Gry", True, (255, 255, 255))
            self.screen.blit(title, (self.s_width // 2 - title.get_width() // 2, 100))

            # Draw subtitle
            subtitle_font = load_font("arial", 24)
            subtitle = subtitle_font.render("Wybierz rozdzielczość ekranu:", True, (200, 200, 200))
            self.screen.blit(subtitle, (self.s_width // 2 - subtitle.get_width() // 2, 150))

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    submenu_running = False
                    return
                if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                    submenu_running = False
                    return

                # Handle button events
                for button in buttons:
                    if button.handle_event(event, mouse_pos):
                        if button == back_button:
                            submenu_running = False
                            return
                        # Resolution change will be handled by button action

            # Update and draw buttons
            for button in buttons:
                button.update(mouse_pos)
                button.draw(self.screen)

            # Update cursor based on hover states
            cursor_manager.update_cursor()

            pygame.display.update()

    def change_resolution(self, width, height):
        """Change the game resolution"""
        try:
            # Store previous resolution for fallback
            prev_width, prev_height = self.s_width, self.s_height
            prev_screen = self.screen

            if width == "fullscreen":
                # Switch to fullscreen - use desktop resolution
                self.screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
                self.is_fullscreen = True
            else:
                # Switch to windowed mode with specific resolution
                # Get desktop resolution by temporarily switching to fullscreen
                temp_fullscreen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
                desktop_width, desktop_height = temp_fullscreen.get_size()

                # Only clamp if the requested resolution is larger than the desktop
                if width > desktop_width:
                    width = desktop_width - 100  # Leave some margin for window decorations
                if height > desktop_height:
                    height = desktop_height - 100

                self.screen = pygame.display.set_mode((width, height))
                self.is_fullscreen = False

            # Update screen dimensions
            new_width, new_height = self.screen.get_size()

            # Validate the resolution change was successful
            if new_width <= 0 or new_height <= 0:
                raise Exception("Invalid screen dimensions")

            self.s_width, self.s_height = new_width, new_height

            # Update resolution manager if available
            try:
                from resolution_manager import get_resolution_manager
                resolution_manager = get_resolution_manager()
                resolution_manager.update_resolution(self.s_width, self.s_height)
            except ImportError:
                pass  # Resolution manager not available

            # Refresh all UI elements and backgrounds for new resolution
            self._refresh_ui_for_resolution()

            # Show confirmation message
            resolution_text = "Pełny ekran" if width == "fullscreen" else f"{width}x{height}"
            self.show_message(f"Rozdzielczość zmieniona na: {resolution_text}")

        except Exception as e:
            print(f"Error changing resolution: {e}")
            # Restore previous resolution on failure
            try:
                self.screen = prev_screen
                self.s_width, self.s_height = prev_width, prev_height
            except:
                # If restoration fails, try to set a safe default
                self.screen = pygame.display.set_mode((1024, 768))
                self.s_width, self.s_height = 1024, 768
                self.is_fullscreen = False
            self.show_message("Błąd przy zmianie rozdzielczości!")

    def _refresh_ui_for_resolution(self):
        """Refresh UI elements and backgrounds after resolution change"""
        # This method can be extended to refresh specific UI elements
        # For now, it serves as a placeholder for future enhancements

        # Clear any cached UI elements that might need repositioning
        try:
            from resolution_manager import get_resolution_manager
            resolution_manager = get_resolution_manager()
            # UI elements will be repositioned automatically by the resolution manager
            pass
        except ImportError:
            pass

    def show_message(self, message):
        """Show a temporary message to the user"""
        # Get scaled font size based on current resolution
        try:
            from resolution_manager import get_resolution_manager
            resolution_manager = get_resolution_manager()
            font_size = resolution_manager.get_scaled_font_size(36)
        except ImportError:
            font_size = 36

        # Try Segoe UI (Windows default), then Arial, then default
        try:
            font = pygame.font.SysFont("Segoe UI", font_size)
        except Exception:
            try:
                font = pygame.font.SysFont("Arial", font_size)
            except Exception:
                font = pygame.font.SysFont(None, font_size)
        text = font.render(message, True, (255, 255, 255))

        # Show message for 2 seconds
        start_time = pygame.time.get_ticks()
        while pygame.time.get_ticks() - start_time < 2000:
            # Create a new background with current resolution
            background = Background('background', self.s_width, self.s_height)
            background.draw(self.screen)

            # Center the message
            text_x = (self.s_width - text.get_width()) // 2
            text_y = (self.s_height - text.get_height()) // 2

            # Scale padding based on resolution
            try:
                padding = resolution_manager.get_scaled_spacing(20)
            except:
                padding = 20

            screen_rect = pygame.Rect(text_x - padding, text_y - padding,
                                    text.get_width() + 2 * padding, text.get_height() + 2 * padding)
            pygame.draw.rect(self.screen, (40, 40, 40), screen_rect)
            pygame.draw.rect(self.screen, (200, 200, 200), screen_rect, 2)
            self.screen.blit(text, (text_x, text_y))

            pygame.display.update()

            # Handle events to prevent freezing
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    return

    def single_player(self):
        # Load user data first to get current level
        try:
            with open(resource_path('data/profile.json'), encoding='utf-8') as f:
                user_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Error loading profile: {e}")
            self.show_message("Błąd wczytywania profilu!")
            return

        # Fix any corrupted level progression data
        user_data = self.fix_level_progression(user_data)

        # Save the fixed data back to profile
        with open(resource_path('data/profile.json'), 'w', encoding='utf-8') as f:
            json.dump(user_data, f, indent=4)

        # Use race_level for opponent selection, default to player level if not set
        try:
            current_level = user_data.get('race_level', user_data["level"]['current'])
        except KeyError:
            print("Invalid profile data structure")
            self.show_message("Nieprawidłowe dane profilu!")
            return

        # Show level info screen
        level_info_result = draw_level_info_screen(self.s_width, self.s_height, self.screen, current_level)

        if level_info_result == "quit":
            self.running = False
            return
        elif level_info_result == "back":
            return
        elif level_info_result != "start_race":
            return

        # Now start the actual race
        bg = Map('map1', self.s_width, self.s_height)
        map_distance = bg.width * 2.5 - self.s_width
        distance_covered = 0
        is_map_ended = False

        try:
            with open(resource_path('data/garage.json'), encoding='utf-8') as f:
                cars_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Error loading garage data: {e}")
            self.show_message("Błąd wczytywania danych garaży!")
            return

        try:
            with open(resource_path('data/oponent_levels.json'), encoding='utf-8') as f:
                oponent_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Error loading opponent data: {e}")
            self.show_message("Błąd wczytywania danych przeciwników!")
            return

        # Validate selected car index
        selected_car_index = user_data["cars"].get("selected_car", 0)
        if selected_car_index < 0 or selected_car_index >= len(cars_data):
            print(f"Invalid selected car index: {selected_car_index}")
            self.show_message("Nieprawidłowy indeks samochodu!")
            return

        selected_car_data = cars_data[selected_car_index]

        # Ensure we don't exceed available levels
        max_level = len(oponent_data)
        if current_level > max_level:
            current_level = max_level

        opponent_data = oponent_data[current_level - 1]

        # Color system removed - no color handling needed

        start_time = time.time()
        selected_car_index = user_data["cars"]["selected_car"]
        player = Car(
            selected_car_data["name"],
            None,  # Color system removed
            selected_car_data["weight"],
            selected_car_data["parts"],
            100, self.s_height // 5 * 3,
            start_time,
            selected_car_index  # Pass car index for condition tracking
        )
        opponent = OpponentCar(
            opponent_data["name"],
            "0",  # Use color index directly with new system
            opponent_data["weight"],
            opponent_data["parts"],
            100, self.s_height // 5 * 3 + 100,
            start_time,
            opponent_data.get("opponent_name", f"Poziom {current_level}")
        )
        timer = TimeToStart()

        # Initialize player stats from user_data (ONCE, not in every frame)
        player_level = user_data['level']['current']
        player_exp = user_data['level']['exp']
        player_money = user_data['money']

        while self.running:
            dt = self.clock.tick(60) / 1000.0
            keys = pygame.key.get_pressed()
            is_map_ended = bg.is_map_ended(map_distance)

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False

            self.screen.fill((20, 235, 35))
            bg.draw(self.screen)

            if timer.time > 0:
                timer.update_time()
                # Draw cars during countdown but don't allow movement
                player.update(None, None)
                opponent.update(0)  # No movement during countdown
                player.draw(self.screen)
                opponent.draw(self.screen, distance_covered, is_map_ended, bg.x_cord)
                timer.draw(self.screen, self.s_width, self.s_height)
                pygame.display.update()  # Update display during countdown
            else:
                player.update(keys, dt)
                opponent.update(dt)

                if not is_map_ended:
                    scroll_speed = player.speed * dt
                    bg.tick(scroll_speed)
                    distance_covered += scroll_speed
                    player.x_cord = 100
                else:
                    bg.x_cord = -map_distance

                player.draw(self.screen)
                opponent.draw(self.screen, distance_covered, is_map_ended, bg.x_cord)
                if opponent.elapsed_time(map_distance):
                    # Player lost - but still get participation reward (rebalanced)
                    base_reward = 50 + (current_level * 30)  # Reduced participation reward

                    # Calculate performance bonus safely
                    opponent_time = opponent.elapsed_time(map_distance)
                    player_time = player.elapsed_time(map_distance)

                    if player_time is not None and player_time > 0:
                        # Player finished but lost - calculate based on time difference
                        performance_bonus = max(1.0, 1.5 - (opponent_time / player_time))
                    else:
                        # Player didn't finish - minimal bonus
                        performance_bonus = 1.0

                    reward = int(base_reward * performance_bonus)
                    # Significantly reduced XP for losing (more balanced)
                    exp_gain = int(reward * (0.1 + min(player_level, 50) / 200))  # Much lower XP gain, capped scaling
                    player_exp += exp_gain

                    # Update user_data with race rewards BEFORE calling handle_level_up
                    user_data['money'] = player_money + reward

                    # Handle level progression correctly with rewards
                    player_level, _, levels_gained, level_rewards = self.handle_level_up(player_exp, player_level, user_data)

                    # Now user_data['money'] contains both race rewards and level rewards
                    player_money = user_data['money']

                    # Update all race-related data
                    race_time = opponent.elapsed_time(map_distance)
                    self.update_race_data_after_finish(user_data, player_level, player_exp, player_money, selected_car_index, selected_car_data, race_time, "normal_driving")

                    # Show level-up screen if player leveled up
                    if levels_gained:
                        level_up_result = draw_level_up_screen(self.s_width, self.s_height, self.screen, levels_gained, level_rewards)
                        if level_up_result == "quit":
                            self.running = False
                            return

                    return draw_end_screen(None, opponent.elapsed_time(map_distance), False, self.screen, self.s_width, reward, player_level)

                if player.elapsed_time(map_distance):
                    # Player won - advance to next level and give better rewards

                    # Calculate sophisticated reward system (rebalanced)
                    base_win_reward = 150 + (current_level * 100)  # Further reduced base reward
                    time_bonus = max(1.0, 1.8 - player.elapsed_time(map_distance) / 20.0)  # Further reduced time bonus
                    level_multiplier = 1.0 + (current_level * 0.12)  # Further reduced level multiplier

                    # Calculate opponent difficulty bonus
                    with open(resource_path('data/oponent_levels.json'), 'r', encoding='utf-8') as f:
                        opponent_data = json.load(f)

                    if current_level <= len(opponent_data):
                        opponent_car = opponent_data[current_level - 1]
                        opponent_parts = opponent_car.get('parts', {})

                        # Calculate opponent car value for difficulty bonus
                        opponent_value = 0
                        for _, part_data in opponent_parts.items():
                            if part_data:
                                opponent_value += part_data.get('value', 0)

                        difficulty_bonus = 1.0 + (opponent_value / 10000)  # Bonus based on opponent car value
                    else:
                        difficulty_bonus = 1.0

                    reward = int(base_win_reward * time_bonus * level_multiplier * difficulty_bonus)
                    # Reduced XP for winning (more balanced progression)
                    exp_gain = int(reward * (0.2 + min(player_level, 50) / 100))  # Much more balanced, capped scaling
                    player_exp += exp_gain

                    # Update user_data with race rewards BEFORE calling handle_level_up
                    user_data['money'] = player_money + reward

                    # Advance to next race level (different from player level)
                    max_race_level = len(oponent_data)
                    if current_level < max_race_level:
                        # Update the race level in profile
                        user_data['race_level'] = user_data.get('race_level', current_level) + 1

                    # Handle level progression correctly with rewards
                    player_level, _, levels_gained, level_rewards = self.handle_level_up(player_exp, player_level, user_data)

                    # Now user_data['money'] contains both race rewards and level rewards
                    player_money = user_data['money']

                    # Update all race-related data
                    race_time = player.elapsed_time(map_distance)
                    self.update_race_data_after_finish(user_data, player_level, player_exp, player_money, selected_car_index, selected_car_data, race_time, "aggressive_driving")

                    # Show level-up screen if player leveled up
                    if levels_gained:
                        level_up_result = draw_level_up_screen(self.s_width, self.s_height, self.screen, levels_gained, level_rewards)
                        if level_up_result == "quit":
                            self.running = False
                            return

                    return draw_end_screen(player.elapsed_time(map_distance), None, True, self.screen, self.s_width, reward, player_level)

                pygame.display.update()
